{"name": "backend", "version": "1.0.0", "main": "src/server.js", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js"}, "keywords": [], "type": "module", "author": "", "license": "ISC", "description": "", "dependencies": {"@neondatabase/serverless": "^1.0.0", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.9", "cors": "^2.8.5", "cron": "^4.3.0", "dotenv": "^16.5.0", "express": "^4.21.0"}, "devDependencies": {"nodemon": "^3.1.10"}}