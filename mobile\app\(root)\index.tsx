import { SignedIn, SignedOut, useUser } from "@clerk/clerk-expo";
import { Link } from "expo-router";
import { Image, Text, View } from "react-native";
import { SignOutButton } from "@/components/SignOutButton";
import { useTransactions } from "@/hooks/useTransactions";
import { useEffect } from "react";
import PageLoader from "@/components/PageLoder";
import { styles } from "@/assets/styles/home.styles";

export default function Page() {
  const { user } = useUser();
  const { deleteTransaction, transactions, summary, isLoading, loadData } = useTransactions(user?.id);

  useEffect(() => {
    loadData();
  }, [loadData]);

  if (isLoading) return <PageLoader />;

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          {/* Left */}
          <View style={styles.headerLeft}>
            <Image source={require("../../assets/images/logo.png")} style={styles.headerLogo} resizeMode="contain" />
            <View style={styles.welcomeContainer}>
              <Text style={styles.welcomeText}>Welcome</Text>
              <Text style={styles.welcomeText}>{user?.emailAddresses[0]?.emailAddress.split("@")[0]}</Text>
            </View>
          </View>
          {/* Right */}
        </View>
      </View>
    </View>
  );
}
